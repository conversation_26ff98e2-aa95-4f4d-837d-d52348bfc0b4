<template>
    <el-dialog v-model="isShow" width="420" :close-on-click-modal="false" :close-on-press-escape="false">
        <template #header>
            <div class="title">使用者承诺须知</div>
        </template>
        <div class="info">
            当前克隆的音色未保存，返回后需重新生成，确认要返回吗当前克隆的音色未保存，返回后需重新生成，确认要返回吗当前克隆的音色未保存，返回后需重新生成，确认要返回吗当前克隆的音色未保存，返回后需重新生成，确认要返回吗当前克隆的音色未保存，返回后需重新生成，确认要返回吗当前克隆的音色未保存，返回后需重新生成，确认要返回吗当前克隆的音色未保存，返回后需重新生成，确认要返回吗当前克隆的音色未保存，返回后需重新生成，确认要返回吗当前克隆的音色未保存，返回后需重新生成，确认要返回吗当前克隆的音色未保存，返回后需重新生成，确认要返回吗当前克隆的音色未保存，返回后需重新生成，确认要返回吗当前克隆的音色未保存，返回后需重新生成，确认要返回吗当前克隆的音色未保存，返回后需重新生成，确认要返回吗
        </div>
        <div class="footer">
            <div class="confirm" :class="{ active: num == 0 }" @click="confirm('confirm')">我已知晓，同意（{{ num }}s）</div>
            <div class="cancel" @click="confirm('cancel')">取消</div>
        </div>
    </el-dialog>
</template>

<script setup>
import { watch, ref, onMounted, onBeforeUnmount } from 'vue';

const props = defineProps({ visible: { type: Boolean, default: false } })
const emit = defineEmits(['update:visible'])

const isShow = ref(props.dialogAgreementVisible)
const num = ref(3)

let timer = null
const startCount = () => {
    clearInterval(timer)
    timer = null
    num.value = 3
    timer = setInterval(() => {
        num.value--
        if (num.value == 0) {
            console.log('num.value', num.value)
            clearInterval(timer)
            timer = null
        }
    }, 1000)
}
onMounted(() => {
    startCount()
})
onBeforeUnmount(() => {
    if (timer) {
        clearInterval(timer)
        timer = null
    }
})
const confirm = (val) => {
    if (val == 'confirm' && num.value == 0) {
        localStorage.setItem('userAgreementProtocol', true)
    }
    if (timer) {
        clearInterval(timer)
        timer = null
    }
    isShow.value = false
    emit('update:visible', false)
}

watch(() => props.visible, (newVal) => {
    isShow.value = newVal
    if (newVal) {
        startCount()
    } else {
        clearInterval(timer)
        timer = null
    }
}, { immediate: true, deep: true })

</script>

<style scoped lang="scss">
* {
    box-sizing: border-box;
}

.title {
    font-size: 16px;
    color: #000000;
    font-weight: 500;
}

.info {
    font-size: 12px;
    color: #1E1E1E;
    line-height: 2;
    letter-spacing: 1px;
}

.footer {
    margin: 40px 0 0 0;

    div {
        height: 40px;
        border-radius: 4px;
        text-align: center;
        line-height: 40px;
        color: #fff;
        font-size: 14px;
        font-weight: 500;
        float: right;
    }

    .cancel {
        width: 96px;
        background: #D3D3D2;
        margin-right: 10px;
        cursor: pointer;
    }

    .confirm {
        width: 163px;
        background: rgba(10, 175, 96, 0.75);
    }

    .active {
        background: #0AAF60;
        cursor: pointer;
    }
}
</style>
<style lang="scss">
.el-dialog__headerbtn .el-dialog__close {
    display: none;
}
</style>